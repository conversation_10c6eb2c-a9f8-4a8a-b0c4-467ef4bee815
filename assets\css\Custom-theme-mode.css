/**
 * Custom Tutor Card ve Theme Mode CSS
 * <PERSON><PERSON> dosya, tutor-card sın<PERSON><PERSON><PERSON> için border ve box-shadow dü<PERSON>lemelerini ve
 * tema modu geçiş butonu için stil ve animasyonları içerir.
 *
 * @package DmrLMS
 * @since 1.0.4
 */

/* <PERSON><PERSON> kartları için border ve box-shadow düzenlemeleri */
.tutor-card:not(.tutor-no-border) {
    border: 1px solid #cdcfd536 !important;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.05) !important;
}

/* Tema modu geçiş butonu animasyonları */
@keyframes theme-toggle-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(180deg);
    }
}

@keyframes theme-toggle-scale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}

/* Güneş-Hilal geçiş animasyonu */
@keyframes sun-to-moon {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(0.5) rotate(90deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }
}

@keyframes moon-to-sun {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(0.5) rotate(-90deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Tema değişikliği sırasında sayfa geçiş efekti */
.theme-transition-active {
    animation: theme-transition-fade 0.5s ease;
}

@keyframes theme-transition-fade {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        opacity: 1;
    }
}
