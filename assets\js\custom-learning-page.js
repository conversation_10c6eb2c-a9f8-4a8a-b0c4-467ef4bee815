/**
 * Tutor LMS Custom Learning Page JavaScript
 * Bu dosya, kurs izleme ekranıyla ilgili tüm JavaScript işlevlerini birleştirir.
 *
 * İÇERİK:
 * 1. Admin Bar Margin Ayarlayıcı
 * 2. <PERSON>rs Arka Plan Sınıfı Ekleyici
 * 3. <PERSON><PERSON> Çeviriler
 * 4. Yorum Cevaplama Sistemi
 * 5. Kurs İçeriği Arama
 * 6. Video Yönlendirme ve Tam Ekran İşleyici
 * 7. Arama Fonksiyonları
 * 8. Sidebar Overlay
 * 9. Sidebar Toggle
 * 10. Video Oynatıcı Modern
 * 11. Video Toggle Optimize Edilmiş
 * 12. Ders Tamamlama İşlemleri
 */

(function($) {
    'use strict';

    // DOM yüklendiğinde çalışacak
    $(document).ready(function() {
        // Tüm işlevleri başlat
        initAdminBarMargin();
        initCourseBackground();
        initCustomTranslations();
        initCommentReplySystem();
        initCourseContentSearch();
        initOrientationFullscreen();
        initSearchFunctions();
        initSidebarOverlay();
        initSidebarToggle();
        initModernVideoPlayer();
        initVideoToggle();
        initDarkModeSupport();
    });

    /********************************************
     * 1. ADMIN BAR MARGIN AYARLAYICI
     ********************************************/
    function initAdminBarMargin() {
        // Admin barın varlığını kontrol et
        const adminBarExists = document.getElementById('wpadminbar') !== null;

        // Sidebar başlık elementini bul
        const sidebarTitleElements = document.querySelectorAll('.tutor-course-single-sidebar-title');

        // Ekran genişliğini kontrol et (1199px ve altı mobil/tablet olarak kabul edilir)
        const isMobileOrTablet = window.innerWidth <= 1199;

        // Her sidebar başlık elementine uygun margin-top değerini ekle
        sidebarTitleElements.forEach(function(element) {
            if (adminBarExists) {
                if (isMobileOrTablet) {
                    // Mobil/tablet cihazlarda admin bar varsa 50px margin-top ekle
                    element.style.marginTop = '50px';
                } else {
                    // Masaüstü cihazlarda admin bar varsa 30px margin-top ekle
                    element.style.marginTop = '30px';
                }
            } else {
                // Admin bar yoksa margin-top'ı sıfırla
                element.style.marginTop = '0px';
            }
        });

        // Ekran boyutu değiştiğinde margin'i güncelle
        window.addEventListener('resize', function() {
            const isNowMobileOrTablet = window.innerWidth <= 1199;

            // Ekran boyutu kategorisi değiştiyse margin'i güncelle
            if (isNowMobileOrTablet !== isMobileOrTablet && adminBarExists) {
                sidebarTitleElements.forEach(function(element) {
                    if (isNowMobileOrTablet) {
                        element.style.marginTop = '50px';
                    } else {
                        element.style.marginTop = '30px';
                    }
                });
            }
        });
    }

    /********************************************
     * 2. KURS ARKA PLAN SINIFI EKLEYİCİ
     ********************************************/
    function initCourseBackground() {
        // Check if we're on a course viewing page
        const isCourseViewingPage =
            document.querySelector('.tutor-course-single-content-wrapper') ||
            document.querySelector('.tutor-single-lesson-wrap') ||
            document.querySelector('.tutor-single-course-wrap') ||
            document.querySelector('.tutor-course-single-sidebar-wrapper');

        // If we're on a course viewing page, add a specific class to body
        if (isCourseViewingPage) {
            document.body.classList.add('tutor-course-viewing-page');
        }
    }

    /********************************************
     * 3. ÖZEL ÇEVİRİLER
     ********************************************/
    function initCustomTranslations() {
        // Change "Mark as Complete" text to "Dersi tamamla"
        const markCompleteButtons = document.querySelectorAll('.tutor-topbar-mark-btn span:not([class^="tutor-icon-"])');

        markCompleteButtons.forEach(function(button) {
            if (button.textContent.trim() === 'Mark as Complete') {
                button.textContent = 'Dersi tamamla';
            }
        });

        // Change course viewing tab texts to Turkish
        const tabTexts = {
            'Overview': 'Genel Bakış',
            'Exercise Files': 'Ders Dosyaları',
            'Comments': 'Yorumlar'
        };

        // Select all tab navigation links
        const tabLinks = document.querySelectorAll('.tutor-course-spotlight-nav .tutor-nav-link span:not([class^="tutor-icon-"])');

        // Replace the text content of each tab
        tabLinks.forEach(function(span) {
            const originalText = span.textContent.trim();
            if (tabTexts[originalText]) {
                span.textContent = tabTexts[originalText];
            }
        });

        // Also change the headings inside the tabs
        const exerciseFilesHeading = document.querySelector('#tutor-course-spotlight-files .tutor-fs-5.tutor-fw-medium.tutor-color-black');
        if (exerciseFilesHeading && exerciseFilesHeading.textContent.trim() === 'Exercise Files') {
            exerciseFilesHeading.textContent = 'Ders Dosyaları';
        }

        const aboutLessonHeading = document.querySelector('#tutor-course-spotlight-overview .tutor-fs-5.tutor-fw-medium.tutor-color-black');
        if (aboutLessonHeading && aboutLessonHeading.textContent.trim() === 'About Lesson') {
            aboutLessonHeading.textContent = 'Ders Hakkında';
        }
    }

    /********************************************
     * 4. YORUM CEVAPLAMA SİSTEMİ
     ********************************************/
    function initCommentReplySystem() {
        // Cevapla butonlarına tıklama olayı ekle
        $(document).on('click', '.tutor-comment-actions span', function(e) {
            e.preventDefault();

            // En yakın üst yorum container'ını bul
            const commentContainer = $(this).closest('.tutor-comments-list');

            // Tüm diğer açık cevapları kapat
            $('.tutor-comments-list.show-replies').not(commentContainer).removeClass('show-replies');

            // Bu yorumun cevaplarını aç/kapat (toggle)
            commentContainer.toggleClass('show-replies');
        });

        // Yorum gönderildikten sonra formu gizle ve cevapları göster
        $(document).on('submit', 'form[tutor-comment-reply]', function() {
            // Form submit işlemini engelleme (varsayılan Tutor LMS davranışı devam etsin)
            return true;
        });
    }

    /********************************************
     * 5. KURS İÇERİĞİ ARAMA
     ********************************************/
    // Değişkenler
    let isSearchActive = false;
    let originalStates = {};

    function initCourseContentSearch() {
        const searchInput = $('#tutor-course-content-search');
        const searchIcon = $('#tutor-course-search-icon');

        if (!searchInput.length) {
            return;
        }

        // İlk yüklemede topiclerin durumunu kaydet
        saveOriginalStates();

        // Accordion başlıklarına tıklandığında durumları güncelle - debounce ile optimize edildi
        let accordionTimeout;
        $('.tutor-accordion-item-header').on('click', function() {
            // Arama aktif değilse durumları güncelle
            if (!isSearchActive) {
                clearTimeout(accordionTimeout);
                accordionTimeout = setTimeout(saveOriginalStates, 300);
            }
        });

        // Arama ikonu tıklama olayı
        searchIcon.on('click', function() {
            // Eğer arama kutusunda yazı varsa ve ikon çarpı ise temizle
            if (searchInput.val().length > 0) {
                searchInput.val('');
                searchInput.trigger('keyup'); // Arama temizlendiğinde keyup event'i tetikle
                toggleSearchIcon(false);
            }
        });

        // Arama yapma olayını dinle - debounce ile optimize edildi
        let searchTimeout;
        searchInput.on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            // İkonu güncelle
            toggleSearchIcon(searchTerm.length > 0);

            // Debounce: Kullanıcı yazmayı bitirene kadar bekle
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                // Arama başladıysa ve aktif değilse
                if (searchTerm.length > 0 && !isSearchActive) {
                    saveOriginalStates();
                    isSearchActive = true;
                }

                // Arama temizlendiyse
                if (searchTerm.length === 0) {
                    isSearchActive = false;
                    restoreOriginalStates();
                } else {
                    // Aramaya devam ediliyorsa
                    showOnlyMatchingContent(searchTerm);
                }
            }, 300);
        });
    }

    // Arama ikonunu değiştir
    function toggleSearchIcon(isActive) {
        const searchIcon = $('#tutor-course-search-icon');

        if (isActive) {
            searchIcon.removeClass('tutor-icon-search').addClass('tutor-icon-times');
        } else {
            searchIcon.removeClass('tutor-icon-times').addClass('tutor-icon-search');
        }
    }

    // Topiclerin orijinal durumlarını sakla
    function saveOriginalStates() {
        originalStates = {};

        $('.tutor-course-topic').each(function() {
            const $topic = $(this);
            const topicId = $topic.attr('id') || $topic.index();
            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');

            originalStates[topicId] = {
                isOpen: $header.hasClass('is-active'),
                bodyDisplay: $body.css('display'),
                hasDisplayNone: $body.hasClass('tutor-display-none')
            };
        });
    }

    // Topicleri orijinal durumlarına geri getir
    function restoreOriginalStates() {
        // Arama ile ilgili tüm CSS sınıflarını temizle
        document.body.classList.remove('tutor-search-active', 'tutor-search-no-results');

        // Tüm arama sınıflarını kaldır
        $('.tutor-search-hidden').removeClass('tutor-search-hidden');
        $('.tutor-search-visible').removeClass('tutor-search-visible');

        // Önce tüm topic içeriklerini görünür yap
        $('.tutor-course-topic-item').show();
        $('.tutor-course-topic').show();

        // "Arama sonucu bulunamadı" mesajını gizle
        $('.tutor-no-search-results').hide();

        // Şimdi topicleri orijinal durumlarına getir
        $('.tutor-course-topic').each(function() {
            const $topic = $(this);
            const topicId = $topic.attr('id') || $topic.index();
            const state = originalStates[topicId];

            if (!state) return;

            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');

            // Orijinal açık/kapalı durumuna getir
            if (state.isOpen) {
                $header.addClass('is-active');
                $body.removeClass('tutor-display-none');
                $body.css('display', state.bodyDisplay || 'block');
            } else {
                $header.removeClass('is-active');
                if (state.hasDisplayNone) {
                    $body.addClass('tutor-display-none');
                }
                $body.css('display', state.bodyDisplay || 'none');
            }
        });
    }

    // Sadece aranan içeriğe uyan topic ve içerikleri göster - optimize edilmiş
    function showOnlyMatchingContent(searchTerm) {
        // Performans için DOM seçicilerini önbelleğe al
        const $contentItems = $('.tutor-course-topic-item');
        const $topics = $('.tutor-course-topic');
        const $sidebarWrapper = $('.tutor-course-single-sidebar-wrapper');

        // CSS sınıfları kullanarak görünürlük kontrolü
        if (!document.getElementById('tutor-search-style')) {
            const style = document.createElement('style');
            style.id = 'tutor-search-style';
            style.textContent = `
                .tutor-search-hidden { display: none !important; }
                .tutor-search-visible { display: block !important; }
                .tutor-search-active .tutor-accordion-item-header.is-active + .tutor-accordion-item-body {
                    display: block !important;
                }
                .tutor-search-no-results .tutor-course-topic-item {
                    display: none !important;
                }
                .tutor-no-search-results {
                    display: none;
                    padding: 30px 20px;
                    text-align: center;
                    background-color: #fff;
                    border-radius: 8px;
                    margin: 20px 10px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                    transition: opacity 0.3s ease;
                    opacity: 0;
                }
                .tutor-search-no-results .tutor-no-search-results {
                    display: block;
                    opacity: 1;
                }
                .tutor-no-search-results-icon {
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #f0f5ff;
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    margin-left: auto;
                    margin-right: auto;
                }
                .tutor-no-search-results-icon i {
                    font-size: 15px;
                    color: var(--tutor-color-primary);
                    display: inline-block;
                    line-height: 1;
                }
                .tutor-no-search-results-text {
                    font-size: 16px;
                    color: #3C4C5A;
                    margin-bottom: 8px;
                    font-weight: 500;
                }
                .tutor-no-search-results-hint {
                    font-size: 14px;
                    color: #6a7c8f;
                    line-height: 1.5;
                    max-width: 250px;
                    margin: 0 auto;
                }
            `;
            document.head.appendChild(style);
        }

        // "Arama sonucu bulunamadı" mesajı elementini kontrol et ve yoksa oluştur
        let noResultsElement = document.querySelector('.tutor-no-search-results');

        if (!noResultsElement) {
            noResultsElement = document.createElement('div');
            noResultsElement.className = 'tutor-no-search-results';
            noResultsElement.innerHTML = `
                <div class="tutor-no-search-results-icon">
                    <i class="tutor-icon-search"></i>
                </div>
                <div class="tutor-no-search-results-text">
                    Arama sonucu bulunamadı
                </div>
                <div class="tutor-no-search-results-hint">
                    Farklı anahtar kelimeler deneyebilir veya filtreleri temizleyebilirsiniz
                </div>
            `;
            $sidebarWrapper.append(noResultsElement);
        } else {
            // Element zaten varsa, görünür yap
            $(noResultsElement).show();
        }

        // Arama aktif olduğunu belirt
        document.body.classList.add('tutor-search-active');

        // Eşleşen topic listesi - Set kullanarak performans artışı
        const matchedTopics = new Set();
        let hasMatches = false;

        // Tüm içerik öğelerini gizle - CSS sınıfı kullanarak
        $contentItems.addClass('tutor-search-hidden').removeClass('tutor-search-visible');

        // Arama terimini içeren içerik öğelerini göster - tek seferde DOM manipülasyonu
        $contentItems.each(function() {
            const $item = $(this);
            const itemTitle = $item.find('.tutor-course-topic-item-title').text().toLowerCase();

            if (itemTitle.includes(searchTerm)) {
                $item.removeClass('tutor-search-hidden').addClass('tutor-search-visible');
                hasMatches = true;

                // Bu içeriğin parent topic'ini bul ve kaydet
                const $topicContainer = $item.closest('.tutor-course-topic');
                matchedTopics.add($topicContainer[0]);
            }
        });

        // Sonuç bulunamadı durumunu işaretle
        if (!hasMatches) {
            document.body.classList.add('tutor-search-no-results');
        } else {
            document.body.classList.remove('tutor-search-no-results');
        }

        // Tüm topicleri kontrol et - toplu DOM manipülasyonu
        $topics.each(function() {
            const $topic = $(this);
            const $header = $topic.find('.tutor-accordion-item-header');
            const $body = $topic.find('.tutor-accordion-item-body');

            if (matchedTopics.has(this)) {
                // Eşleşen içeriğe sahip topic - aç
                $topic.removeClass('tutor-search-hidden').addClass('tutor-search-visible');
                $header.addClass('is-active');
                $body.removeClass('tutor-display-none');
                $body.css('display', 'block');
            } else {
                // Eşleşmeyen topic - gizle
                $topic.addClass('tutor-search-hidden').removeClass('tutor-search-visible');
                $header.removeClass('is-active');
                $body.addClass('tutor-display-none');
                $body.css('display', 'none');
            }
        });
    }
    /********************************************
     * 6. VİDEO YÖNLENDİRME VE TAM EKRAN İŞLEYİCİ
     ********************************************/
    function initOrientationFullscreen() {
        // Video elementlerini seç
        const videoPlayers = document.querySelectorAll('.tutor-video-player');
        const plyrPlayers = document.querySelectorAll('.plyr');

        // Ekran yönü değişikliğini dinle
        window.addEventListener('orientationchange', function() {
            // Yatay modda ise (landscape)
            if (window.orientation === 90 || window.orientation === -90) {
                // Tüm video oynatıcıları kontrol et
                videoPlayers.forEach(function(player) {
                    if (player && typeof player.requestFullscreen === 'function') {
                        player.requestFullscreen().catch(err => {
                            console.log('Tam ekran yapılamadı:', err);
                        });
                    }
                });

                // Plyr oynatıcıları kontrol et
                plyrPlayers.forEach(function(player) {
                    if (player && player.plyr && typeof player.plyr.fullscreen === 'object') {
                        player.plyr.fullscreen.enter().catch(err => {
                            console.log('Plyr tam ekran yapılamadı:', err);
                        });
                    }
                });
            }
        });

        // Tam ekran butonlarını özelleştir
        const fullscreenButtons = document.querySelectorAll('.plyr__control--fullscreen, .tutor-icon-maximize');

        fullscreenButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                const player = button.closest('.tutor-video-player') || button.closest('.plyr');

                if (player) {
                    // Plyr oynatıcı için
                    if (player.classList.contains('plyr')) {
                        if (player.plyr && typeof player.plyr.fullscreen === 'object') {
                            player.plyr.fullscreen.enter().catch(err => {
                                console.log('Plyr tam ekran yapılamadı:', err);

                                // Alternatif yöntem
                                if (player && typeof player.requestFullscreen === 'function') {
                                    player.requestFullscreen().catch(err => {
                                        console.log('Alternatif tam ekran yapılamadı:', err);
                                    });
                                }
                            });
                        }
                    }
                    // Tutor video oynatıcı için
                    else {
                        if (typeof player.requestFullscreen === 'function') {
                            player.requestFullscreen().catch(err => {
                                console.log('Tam ekran yapılamadı:', err);
                            });
                        }
                    }
                }
            });
        });
    }

    /********************************************
     * 7. ARAMA FONKSİYONLARI
     ********************************************/
    function initSearchFunctions() {
        // Arama kutusunu seç
        const searchInput = document.getElementById('tutor-course-content-search');

        // Sidebar wrapper'ı seç
        const sidebarWrapper = document.querySelector('.tutor-course-single-sidebar-wrapper');

        if (searchInput && sidebarWrapper) {
            // Arama kutusuna yazı yazıldığında
            searchInput.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    // Arama yapılıyorsa tutor-searching sınıfını ekle
                    sidebarWrapper.classList.add('tutor-searching');
                } else {
                    // Arama kutusu boşsa tutor-searching sınıfını kaldır
                    sidebarWrapper.classList.remove('tutor-searching');

                    // Arama temizlendiğinde akordiyonları geri yükle
                    if (typeof isSearchActive !== 'undefined' && isSearchActive) {
                        isSearchActive = false;
                        restoreOriginalStates();
                    }
                }
            });

            // Arama kutusu temizlendiğinde
            searchInput.addEventListener('search', function() {
                if (this.value.trim() === '') {
                    // Arama kutusu boşsa tutor-searching sınıfını kaldır
                    sidebarWrapper.classList.remove('tutor-searching');

                    // Arama temizlendiğinde akordiyonları geri yükle
                    if (typeof isSearchActive !== 'undefined' && isSearchActive) {
                        isSearchActive = false;
                        restoreOriginalStates();
                    }
                }
            });

            // Çarpı ikonuna veya arama ikonuna tıklama olayını dinle (delegasyon kullanarak)
            document.addEventListener('click', function(event) {
                // Tıklanan eleman çarpı ikonu mu kontrol et
                if ((event.target.classList.contains('tutor-icon-times') &&
                    (event.target.classList.contains('tutor-search-icon') ||
                     event.target.closest('.tutor-form-control-wrapper'))) ||
                    event.target.id === 'tutor-course-search-icon') {

                    // Arama kutusunu temizle
                    searchInput.value = '';

                    // Arama olayını tetikle
                    const searchEvent = new Event('search');
                    searchInput.dispatchEvent(searchEvent);

                    // tutor-searching sınıfını kaldır
                    sidebarWrapper.classList.remove('tutor-searching');

                    // Arama temizlendiğinde akordiyonları geri yükle
                    if (typeof isSearchActive !== 'undefined' && isSearchActive) {
                        isSearchActive = false;
                        restoreOriginalStates();
                    }
                }
            });

            // Sayfa yüklendiğinde arama kutusu boş değilse
            if (searchInput.value.trim() !== '') {
                sidebarWrapper.classList.add('tutor-searching');
            }
        }
    }

    /********************************************
     * 8. SIDEBAR OVERLAY
     ********************************************/
    function initSidebarOverlay() {
        // Overlay elementi yoksa oluştur
        if (!$('.tutor-sidebar-overlay').length) {
            $('body').append('<div class="tutor-sidebar-overlay"></div>');
        }

        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');
        const sidebar = $('.tutor-course-single-sidebar-wrapper');
        const overlay = $('.tutor-sidebar-overlay');
        const closeButton = $('[tutor-hide-course-single-sidebar]');

        // Hamburger butonlarına tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece mobil/tablet görünümünde overlay'i göster
            if (window.matchMedia('(max-width: 1199px)').matches) {
                // Sidebar açılıyor mu kontrol et
                if (!sidebar.hasClass('tutor-lesson-sidebar-show')) {
                    // Sidebar açılıyor, overlay'i göster ve sidebar'a sınıf ekle
                    sidebar.addClass('tutor-lesson-sidebar-show');
                    overlay.addClass('tutor-sidebar-overlay-show');
                    // Hamburger butonuna aktif sınıfı ekle
                    $(this).addClass('tutor-hamburger-active');
                    $('.tutor-mobile-hamburger-btn').addClass('tutor-hamburger-active');
                } else {
                    // Sidebar kapanıyor, overlay'i gizle ve sidebar'dan sınıfı kaldır
                    sidebar.removeClass('tutor-lesson-sidebar-show');
                    overlay.removeClass('tutor-sidebar-overlay-show');
                    // Hamburger butonundan aktif sınıfı kaldır
                    $('.tutor-mobile-hamburger-btn').removeClass('tutor-hamburger-active');
                    $(this).removeClass('tutor-hamburger-active');
                }
            }
        });

        // Overlay'e tıklama olayı ekle
        overlay.on('click', function(e) {
            // Overlay'e tıklandığında sidebar'ı kapat
            sidebar.removeClass('tutor-lesson-sidebar-show');
            overlay.removeClass('tutor-sidebar-overlay-show');
            // Hamburger butonundan aktif sınıfı kaldır
            hamburgerButtons.removeClass('tutor-hamburger-active');
        });

        // Kapatma butonuna tıklama olayı ekle
        closeButton.on('click', function(e) {
            // Kapatma butonuna tıklandığında overlay'i gizle
            overlay.removeClass('tutor-sidebar-overlay-show');
            // Hamburger butonundan aktif sınıfı kaldır
            hamburgerButtons.removeClass('tutor-hamburger-active');
        });
    }

    /********************************************
     * 9. SIDEBAR TOGGLE
     ********************************************/
    // Değişkenler
    let isSidebarExpanded = false;

    function initSidebarToggle() {
        // Önceki durumu kontrol et
        const savedState = localStorage.getItem('tutorSidebarExpanded');

        // Sayfa yüklendiğinde hemen görünümü uygula
        setTimeout(function() {
            if (savedState === 'true') {
                // Eğer sidebar kapalı olarak kaydedilmişse
                if (!isSidebarExpanded) {
                    // Sidebar'a kapalı sınıfını ekle
                    $('body').addClass('tutor-sidebar-expanded');
                    isSidebarExpanded = true;
                }
            } else if (savedState === 'false') {
                // Eğer sidebar açık olarak kaydedilmişse
                if (isSidebarExpanded) {
                    // Sidebar'dan kapalı sınıfını kaldır
                    $('body').removeClass('tutor-sidebar-expanded');
                    isSidebarExpanded = false;
                }
            }
        }, 500); // Sayfanın yüklenmesi için kısa bir gecikme

        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');

        // Butonlara tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece masaüstü görünümünde geniş moda geçsin
            if (window.matchMedia('(min-width: 1200px)').matches) {
                toggleSidebarStyle();
            }
            // Mobil ve tablet görünümünde herhangi bir şey yapma
            // Varsayılan davranış sadece sidebar'i açıp kapatmak olacak
            else {
                e.preventDefault();
            }
        });

        // Önceki ve sonraki butonlarına tıklama olayı ekle
        $('.tutor-single-course-content-prev a, .tutor-single-course-content-next a').on('click', function() {
            // Sidebar durumunu URL'ye ekle
            const currentUrl = $(this).attr('href');
            const separator = currentUrl.includes('?') ? '&' : '?';
            $(this).attr('href', currentUrl + separator + 'sidebar_state=' + (isSidebarExpanded ? 'expanded' : 'normal'));
        });

        // URL'den sidebar durumunu kontrol et
        const urlParams = new URLSearchParams(window.location.search);
        const sidebarState = urlParams.get('sidebar_state');
        if (sidebarState === 'expanded' && !isSidebarExpanded) {
            // Sidebar'a kapalı sınıfını ekle
            $('body').addClass('tutor-sidebar-expanded');
            isSidebarExpanded = true;
            localStorage.setItem('tutorSidebarExpanded', 'true');
        } else if (sidebarState === 'normal' && isSidebarExpanded) {
            // Sidebar'dan kapalı sınıfını kaldır
            $('body').removeClass('tutor-sidebar-expanded');
            isSidebarExpanded = false;
            localStorage.setItem('tutorSidebarExpanded', 'false');
        }
    }

    // Sidebar ve içerik alanının stilini değiştir
    function toggleSidebarStyle() {
        // Sidebar ve içerik alanı elementlerini seç
        const body = $('body');

        if (!isSidebarExpanded) {
            // Genişletilmiş görünüme geç (sidebar gizli, içerik tam genişlikte)
            body.addClass('tutor-sidebar-expanded');

            // Genişletilmiş durumu kaydet
            localStorage.setItem('tutorSidebarExpanded', 'true');
            isSidebarExpanded = true;
        } else {
            // Normal görünüme geri dön (sidebar görünür, içerik daraltılmış)
            body.removeClass('tutor-sidebar-expanded');

            // Normal durumu kaydet
            localStorage.setItem('tutorSidebarExpanded', 'false');
            isSidebarExpanded = false;
        }
    }

    /********************************************
     * 10. VİDEO OYNATICI MODERN
     ********************************************/
    function initModernVideoPlayer() {
        // Yükleme döndürme simgesini gizle
        $('.tutor-video-player .loading-spinner').hide();

        // Mobil deneyim için özel kontroller ekle
        if (window.matchMedia('(max-width: 767px)').matches) {
            enhanceMobileExperience();
        }

        // Tam ekrana geçerken/çıkarken yumuşak geçiş ekle
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    }

    // Tam ekran değişiklik olaylarını işle
    function handleFullscreenChange() {
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement;

        if (isFullscreen) {
            $(isFullscreen).addClass('tutor-fullscreen-active');
        } else {
            $('.tutor-fullscreen-active').removeClass('tutor-fullscreen-active');
        }
    }

    // Mobil deneyimi özel kontrollerle geliştir
    function enhanceMobileExperience() {
        // Çift dokunma ile ileri/geri sarma işlevini ekle
        $('.tutor-video-player video').each(function() {
            const video = $(this)[0];
            let lastTapTime = 0;

            $(this).on('touchend', function(e) {
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTapTime;

                if (tapLength < 500 && tapLength > 0) {
                    // Çift dokunma algılandı
                    const screenWidth = $(this).width();
                    const tapPosition = e.originalEvent.changedTouches[0].pageX;

                    if (tapPosition < screenWidth / 2) {
                        // Sol taraf - 10 saniye geri git
                        video.currentTime = Math.max(0, video.currentTime - 10);
                    } else {
                        // Sağ taraf - 10 saniye ileri git
                        video.currentTime = Math.min(video.duration, video.currentTime + 10);
                    }

                    e.preventDefault();
                }

                lastTapTime = currentTime;
            });
        });
    }

    /********************************************
     * 11. VİDEO TOGGLE OPTİMİZE EDİLMİŞ
     ********************************************/
    // Değişkenler
    let isVideoExpanded = false;

    function initVideoToggle() {
        // Önceki durumu kontrol et
        const savedState = localStorage.getItem('tutorVideoExpanded');
        if (savedState === 'true') {
            isVideoExpanded = true;
            // Sayfa yüklendiğinde görünümü uygula
            setTimeout(function() {
                toggleVideoPlayerStyle();
            }, 300);
        }

        // Hamburger menü butonlarını seç
        const hamburgerButtons = $('[tutor-course-topics-sidebar-toggler], [tutor-course-topics-sidebar-offcanvas-toggler]');

        // Butonlara tıklama olayı ekle
        hamburgerButtons.on('click', function(e) {
            // Sadece masaüstü görünümünde video stilini değiştir
            if (window.matchMedia('(min-width: 1200px)').matches) {
                e.preventDefault();
                toggleVideoPlayerStyle();
            }
            // Mobil/tablet için hiçbir şey yapma, varsayılan davranışı koru
        });
    }

    // Video oynatıcının stilini değiştir
    function toggleVideoPlayerStyle() {
        // Video oynatıcı elementlerini seç
        const videoPlayer = $('.tutor-video-player');
        const videoWrapper = $('.plyr__video-wrapper');
        const videoPlayerWrapper = $('.tutor-video-player-wrapper-modern');

        if (!isVideoExpanded) {
            // Genişletilmiş görünüme geç
            videoPlayer.css({
                // 'border-radius': '0px',
                // 'width': '100%',
                // 'margin-left': 'auto',
                // 'margin-right': 'auto'
            });

            videoWrapper.css({
                // 'width': '80%',
                // 'margin-left': 'auto',
                // 'margin-right': 'auto',
                // 'border-radius': '0px'
                // 'aspect-ratio': '16 / 9'
            });

            videoPlayerWrapper.css({
                // 'padding': '0'
            });

            // Genişletilmiş durumu kaydet
            localStorage.setItem('tutorVideoExpanded', 'true');
            isVideoExpanded = true;
        } else {
            // Normal görünüme geri dön
            videoPlayer.css({
                'border-radius': '12px',
                'width': '90%',
                'margin-left': 'auto',
                'margin-right': 'auto'
            });

            videoWrapper.css({
                'width': '100%',
                'margin-left': 'auto',
                'margin-right': 'auto',
                'border-radius': '10px'
            });

            videoPlayerWrapper.css({
                'padding': '20px 0'
            });

            // Normal durumu kaydet
            localStorage.setItem('tutorVideoExpanded', 'false');
            isVideoExpanded = false;
        }
    }

    /********************************************
     * 12. DERS TAMAMLAMA İŞLEMLERİ
     ********************************************/

    /********************************************
     * 13. DARK MODE DESTEĞİ
     ********************************************/
    function initDarkModeSupport() {
        // Sayfa yüklendiğinde localStorage'dan tema tercihini kontrol et
        const themeMode = localStorage.getItem('tutor_theme_mode');
        if (themeMode === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.body.classList.add('tutor-dark-mode');
        }
    }
    function initLessonCompletionButton() {
        // Sayfa yüklendiğinde buton durumunu kontrol et ve güncelle
        const initialButton = $('.lesson-completion-btn');
        if (initialButton.length) {
            // HTML özniteliğinden tamamlanma durumunu al
            const initialCompleted = initialButton.attr('data-completed') === 'true';

            // Butonun data özelliğini güncelle (jQuery data cache'i güncelle)
            initialButton.data('completed', initialCompleted ? 'true' : 'false');
        }

        // Ders tamamlama butonuna tıklama olayı ekle
        $(document).on('click', '.lesson-completion-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const lessonId = button.attr('data-lesson-id');
            // HTML özniteliğinden tamamlanma durumunu al, data() yerine attr() kullan
            const isCompleted = button.attr('data-completed') === 'true';

            // Butonun durumunu değiştir (tıklanamaz yap)
            button.prop('disabled', true);

            // Kurs ID'sini al
            const courseId = $('.tutor-course-single-sidebar-title').data('course-id') ||
                            $('.tutor-course-single-content-wrapper').data('course-id') ||
                            $('body').data('course-id') ||
                            $('#tutor-course-id').val() ||
                            $('input[name="course_id"]').val();

            // AJAX isteği gönder
            $.ajax({
                url: tutor_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'toggle_lesson_completion',
                    lesson_id: lessonId,
                    is_completed: isCompleted,
                    course_id: courseId || 0,
                    _wpnonce: tutor_data.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Butonun durumunu güncelle
                        updateButtonState(button, !isCompleted);

                        // Sidebar'daki tamamlanma işaretini güncelle - doğrudan DOM manipülasyonu
                        updateSidebarDirectly(lessonId, !isCompleted);

                        // İlerleme çubuğunu güncelle - doğrudan DOM manipülasyonu
                        updateProgressBarDirectly(response.data);
                    } else {
                        alert('İşlem başarısız: ' + (response.data?.message || 'Bilinmeyen hata'));
                    }

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    alert('Sunucu hatası: ' + error);

                    // Butonu tekrar tıklanabilir yap
                    button.prop('disabled', false);
                }
            });
        });
    }

    /**
     * Butonun durumunu güncelle
     *
     * @param {jQuery} button - Buton elementi
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateButtonState(button, isCompleted) {
        // Butonun data özelliğini güncelle (hem jQuery cache hem de HTML özniteliği)
        button.data('completed', isCompleted ? 'true' : 'false');
        button.attr('data-completed', isCompleted ? 'true' : 'false');

        // Butonun sınıfını güncelle
        if (isCompleted) {
            button.removeClass('tutor-btn-primary').addClass('tutor-btn-success');
            button.find('.completion-btn-text').text('Tamamlandı');
        } else {
            button.removeClass('tutor-btn-success').addClass('tutor-btn-primary');
            button.find('.completion-btn-text').text('Dersi tamamla');
        }

        // Zorla yeniden render için kısa bir süre için butonun görünürlüğünü değiştir
        button.css('opacity', '0.9');
        setTimeout(function() {
            button.css('opacity', '1');
        }, 50);
    }

    /**
     * Sidebar'daki tamamlanma işaretini doğrudan güncelle
     *
     * @param {number} lessonId - Ders ID'si
     * @param {boolean} isCompleted - Tamamlanma durumu
     */
    function updateSidebarDirectly(lessonId, isCompleted) {
        // Ders ID'sine göre sidebar öğesini bul
        const sidebarItem = $('a[data-lesson-id="' + lessonId + '"]').closest('.tutor-course-topic-item');

        if (sidebarItem.length) {
            // Checkbox'u bul
            const checkbox = sidebarItem.find('.tutor-form-check-input');

            if (checkbox.length) {
                // Checkbox'u güncelle - DOM'u yeniden oluşturmak yerine özelliklerini değiştir
                checkbox.prop('checked', isCompleted);

                if (isCompleted) {
                    checkbox.attr('checked', 'checked');
                } else {
                    checkbox.removeAttr('checked');
                }

                // CSS sınıfı ekleyerek görsel güncellemeyi sağla
                sidebarItem.toggleClass('tutor-completed-lesson', isCompleted);
            }
        } else {
            // Alternatif yöntem: Tüm ders öğelerini kontrol et
            $('a[href*="' + lessonId + '"]').each(function() {
                const item = $(this).closest('.tutor-course-topic-item');
                const checkbox = item.find('.tutor-form-check-input');

                if (checkbox.length) {
                    // Checkbox'u güncelle - DOM'u yeniden oluşturmak yerine özelliklerini değiştir
                    checkbox.prop('checked', isCompleted);

                    if (isCompleted) {
                        checkbox.attr('checked', 'checked');
                    } else {
                        checkbox.removeAttr('checked');
                    }

                    // CSS sınıfı ekleyerek görsel güncellemeyi sağla
                    item.toggleClass('tutor-completed-lesson', isCompleted);
                }
            });
        }
    }

    /**
     * İlerleme çubuğunu doğrudan güncelle
     *
     * @param {Object} data - AJAX yanıtından gelen veri
     */
    function updateProgressBarDirectly(data) {
        if (!data || !data.completed_percent) {
            return;
        }

        const progressValue = data.completed_percent;
        const completedLessons = data.completed_lessons;
        const totalLessons = data.total_lessons;
        const courseId = data.course_id;

        // CSS değişkenini doğrudan güncelle - tüm ilerleme çubukları bu değişkeni kullanır
        document.documentElement.style.setProperty('--tutor-progress-value', progressValue + '%');

        // Yüzde metinlerini güncelle - jQuery seçicileri optimize edildi
        $('.progress-percentage .tutor-fw-medium').text(progressValue + '%');

        // Ana ilerleme özetini güncelle (kurs başlığının yanındaki)
        if (completedLessons !== undefined && totalLessons !== undefined) {
            $('.tutor-course-completion-progress-bar-percentage').text(completedLessons + '/' + totalLessons);

            // NOT: Topic başlıklarındaki sayılar artık burada güncellenmez
            // Her topic kendi ders sayısını göstermeli, toplam kurs ders sayısını değil
            // Bu güncelleme updateTopicCompletionCounts() fonksiyonunda yapılır
        }

        // Topic başlıklarının yanındaki tamamlanma sayılarını güncelle - sadece gerekli olduğunda
        if (data.lesson_id && courseId) {
            // Sadece ilgili dersin bulunduğu topic'i güncelle
            updateTopicCompletionCounts(courseId, data.lesson_id);
        } else if (courseId) {
            // Eğer lesson_id yoksa tüm topic'leri güncelle
            updateTopicCompletionCounts(courseId, 0);
        }

        // Animasyon ekle - CSS transition kullanarak daha verimli hale getirildi
        document.body.classList.add('tutor-progress-updating');
        setTimeout(function() {
            document.body.classList.remove('tutor-progress-updating');
        }, 300);
    }

    /**
     * Topic başlıklarının yanındaki tamamlanma sayılarını güncelle
     *
     * @param {number} courseId - Kurs ID'si
     * @param {number} lessonId - Ders ID'si (opsiyonel)
     */
    // Topic güncelleme için debounce mekanizması
    let topicUpdateTimeout = null;
    let pendingTopicUpdates = {};

    function updateTopicCompletionCounts(courseId, lessonId) {
        // Aynı ders için bekleyen güncelleme varsa iptal et
        const updateKey = courseId + '-' + lessonId;
        pendingTopicUpdates[updateKey] = true;

        // Debounce: Kısa süre içinde birden fazla güncelleme isteği gelirse birleştir
        if (topicUpdateTimeout) {
            clearTimeout(topicUpdateTimeout);
        }

        topicUpdateTimeout = setTimeout(function() {
            // Tüm bekleyen güncellemeleri topla
            const updateKeys = Object.keys(pendingTopicUpdates);
            if (updateKeys.length === 0) return;

            // Sadece en son güncellemeyi yap
            const lastUpdate = updateKeys[updateKeys.length - 1].split('-');
            const lastCourseId = lastUpdate[0];
            const lastLessonId = lastUpdate[1];

            // AJAX ile topic tamamlanma sayılarını al
            $.ajax({
                url: tutor_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_topic_completion_counts',
                    course_id: lastCourseId,
                    lesson_id: lastLessonId || 0,
                    _wpnonce: tutor_data.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        // Sadece yanıtta dönen topic'leri güncelle
                        $.each(response.data, function(topicId, counts) {
                            // Önce mevcut değeri al
                            const topicSummary = $('.tutor-course-topic-' + topicId + ' .tutor-course-topic-summary');
                            const newText = counts.completed + '/' + counts.total;

                            // Eğer değer değiştiyse güncelle
                            if (topicSummary.length && topicSummary.text() !== newText) {
                                topicSummary.text(newText);

                                // CSS transition ile animasyon ekle
                                topicSummary.addClass('tutor-topic-updated');
                                setTimeout(function() {
                                    topicSummary.removeClass('tutor-topic-updated');
                                }, 300);
                            }
                        });
                    }

                    // Bekleyen güncellemeleri temizle
                    pendingTopicUpdates = {};
                }
            });
        }, 300); // 300ms debounce süresi
    }

    /**
     * Sayfa yüklendiğinde topic tamamlanma sayılarını doğru şekilde başlat
     */
    function initializeTopicCounts() {
        // Kurs ID'sini al - birden fazla kaynaktan dene
        const courseId = $('.tutor-course-single-sidebar-title').data('course-id') ||
                        $('.tutor-course-single-content-wrapper').data('course-id') ||
                        $('body').data('course-id') ||
                        $('#tutor-course-id').val() ||
                        $('input[name="course_id"]').val();

        if (courseId) {
            // Önce topic'lerin mevcut olup olmadığını kontrol et
            const topicSummaries = $('.tutor-course-topic-summary');

            if (topicSummaries.length > 0) {
                // Topic sayılarının güncellenmesi gerekip gerekmediğini kontrol et
                let needsUpdate = false;

                topicSummaries.each(function() {
                    const text = $(this).text().trim();
                    // Eğer boş veya geçersiz format varsa güncelleme gerekli
                    if (!text || !text.includes('/') || text === '0/0') {
                        needsUpdate = true;
                        return false; // each döngüsünden çık
                    }
                });

                // Sadece gerekli olduğunda güncelle
                if (needsUpdate) {
                    updateTopicCompletionCounts(courseId, 0);
                }
            }
        } else {
            console.warn('DMR LMS: Kurs ID bulunamadı, topic sayıları güncellenemedi');
        }
    }

    // DOM yüklendiğinde ders tamamlama butonunu başlat
    $(document).ready(function() {
        // Ders sayfasında olup olmadığını kontrol et
        if ($('.lesson-completion-btn').length) {
            initLessonCompletionButton();
        }

        // Topic sayılarını başlat - DOM tamamen hazır olduğunda
        initializeTopicCounts();
    });

    // Sayfa tamamen yüklendiğinde de topic sayılarını kontrol et
    $(window).on('load', function() {
        // Sadece topic'ler varsa ve DOM ready'de güncelleme yapılmamışsa kontrol et
        if ($('.tutor-course-topic-summary').length > 0) {
            // Kısa bir kontrol sonrası gerekirse güncelle
            initializeTopicCounts();
        }
    });

})(jQuery);
