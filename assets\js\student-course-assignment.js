/**
 * Student Course Assignment
 * This script handles the course assignment functionality for students
 */

(function($) {
    'use strict';

    // Variables
    let currentStudentId = null;
    let courseListLoaded = false;
    let courseList = [];

    // Initialize
    $(document).ready(function() {
        initCourseAssignment();
    });

    /**
     * Initialize course assignment functionality
     */
    function initCourseAssignment() {
        // Add click event to the "Kurs Tanımla" buttons
        $(document).on('click', '.tutor-assign-course-btn', function(e) {
            e.preventDefault();
            currentStudentId = $(this).data('student-id');
            openCourseAssignmentModal(currentStudentId);
        });

        // Add click event to close the modal
        $(document).on('click', '.tutor-course-assignment-modal-close, .tutor-course-assignment-modal-overlay', function(e) {
            e.preventDefault();
            closeCourseAssignmentModal();
        });

        // Add click event to assign/unassign course buttons
        $(document).on('click', '.tutor-course-assign-btn', function(e) {
            e.preventDefault();
            const courseId = $(this).data('course-id');
            assignCourseToStudent(courseId, currentStudentId);
        });

        $(document).on('click', '.tutor-course-unassign-btn', function(e) {
            e.preventDefault();
            const courseId = $(this).data('course-id');
            unassignCourseFromStudent(courseId, currentStudentId);
        });
    }

    /**
     * Open course assignment modal
     *
     * @param {number} studentId - The student ID
     */
    function openCourseAssignmentModal(studentId) {
        // Create modal if it doesn't exist
        if (!$('#tutor-course-assignment-modal').length) {
            createModal();
        }

        // Show modal
        $('.tutor-course-assignment-modal-overlay').show();
        $('#tutor-course-assignment-modal').show();

        // Load courses if not loaded yet
        if (!courseListLoaded) {
            loadCourses();
        } else {
            // Update enrollment status for the current student
            updateEnrollmentStatus(studentId);
        }
    }

    /**
     * Close course assignment modal
     */
    function closeCourseAssignmentModal() {
        $('.tutor-course-assignment-modal-overlay').hide();
        $('#tutor-course-assignment-modal').hide();
    }

    /**
     * Create modal HTML
     */
    function createModal() {
        const modalHTML = `
            <div class="tutor-course-assignment-modal-overlay"></div>
            <div id="tutor-course-assignment-modal" class="tutor-course-assignment-modal">
                <div class="tutor-course-assignment-modal-header">
                    <h3>${tutor_student_course_assignment.i18n.assign_course}</h3>
                    <a href="#" class="tutor-course-assignment-modal-close">×</a>
                </div>
                <div class="tutor-course-assignment-modal-body">
                    <div class="tutor-course-assignment-search">
                        <input type="text" id="tutor-course-search" placeholder="${tutor_student_course_assignment.i18n.search_courses}">
                    </div>
                    <div class="tutor-course-assignment-list">
                        <div class="tutor-course-assignment-loading">
                            <span>${tutor_student_course_assignment.i18n.loading}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHTML);

        // Add search functionality with improved performance
        $('#tutor-course-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            // Only search if term is at least 2 characters or empty
            if (searchTerm.length === 0 || searchTerm.length >= 2) {
                $('.tutor-course-assignment-item').each(function() {
                    const courseTitle = $(this).find('.tutor-course-assignment-title').text().toLowerCase();
                    if (courseTitle.indexOf(searchTerm) > -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }
        });
    }

    /**
     * Load courses via AJAX
     */
    function loadCourses() {
        $('.tutor-course-assignment-loading').show();
        $('.tutor-course-assignment-list').html('<div class="tutor-course-assignment-loading"><span>' + tutor_student_course_assignment.i18n.loading + '</span></div>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_get_courses_for_assignment',
                _wpnonce: tutor_student_course_assignment.nonce
            },
            success: function(response) {
                if (response.success) {
                    courseList = response.data;
                    courseListLoaded = true;
                    updateEnrollmentStatus(currentStudentId);
                } else {
                    $('.tutor-course-assignment-list').html('<div class="tutor-course-assignment-error">' + response.data + '</div>');
                }
            },
            error: function() {
                $('.tutor-course-assignment-list').html('<div class="tutor-course-assignment-error">' + tutor_student_course_assignment.i18n.error + '</div>');
            }
        });
    }

    /**
     * Update enrollment status for courses
     *
     * @param {number} studentId - The student ID
     */
    function updateEnrollmentStatus(studentId) {
        $('.tutor-course-assignment-loading').show();

        // Log for debugging
        console.log('Updating enrollment status for student ID:', studentId);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_get_student_enrolled_courses',
                student_id: studentId,
                _wpnonce: tutor_student_course_assignment.nonce
            },
            success: function(response) {
                console.log('AJAX response:', response);

                if (response.success) {
                    renderCourseList(courseList, response.data);
                } else {
                    $('.tutor-course-assignment-list').html('<div class="tutor-course-assignment-error">' + response.data + '</div>');
                }
                $('.tutor-course-assignment-loading').hide();
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                $('.tutor-course-assignment-list').html('<div class="tutor-course-assignment-error">' + tutor_student_course_assignment.i18n.error + '</div>');
                $('.tutor-course-assignment-loading').hide();
            }
        });
    }

    /**
     * Render course list with enrollment status
     *
     * @param {Array} courses - List of courses
     * @param {Array} enrolledCourses - List of enrolled course IDs
     */
    function renderCourseList(courses, enrolledCourses) {
        // Make sure enrolledCourses is an array
        const enrolledCoursesArray = Array.isArray(enrolledCourses) ? enrolledCourses : [];

        // Log for debugging
        console.log('Enrolled courses:', enrolledCoursesArray);

        if (courses.length === 0) {
            const html = '<div class="tutor-course-assignment-empty">' + tutor_student_course_assignment.i18n.no_courses + '</div>';
            $('.tutor-course-assignment-list').html(html);
            return;
        }

        // Prepare courses with enrollment status
        const coursesWithStatus = courses.map(function(course) {
            // Convert course ID to integer for comparison
            const courseId = parseInt(course.ID);

            // Check if course ID is in enrolled courses
            let isEnrolled = false;
            for (let i = 0; i < enrolledCoursesArray.length; i++) {
                if (parseInt(enrolledCoursesArray[i]) === courseId) {
                    isEnrolled = true;
                    break;
                }
            }

            // Return course with enrollment status
            return {
                ...course,
                isEnrolled: isEnrolled
            };
        });

        // Sort courses: enrolled courses first, then alphabetically
        coursesWithStatus.sort(function(a, b) {
            // First sort by enrollment status (enrolled courses first)
            if (a.isEnrolled && !b.isEnrolled) return -1;
            if (!a.isEnrolled && b.isEnrolled) return 1;

            // Then sort alphabetically by title
            return a.post_title.localeCompare(b.post_title);
        });

        // Generate HTML
        let html = '';
        coursesWithStatus.forEach(function(course) {
            // Log for debugging
            console.log('Course ID:', course.ID, 'Is enrolled:', course.isEnrolled);

            const buttonClass = course.isEnrolled ? 'tutor-course-unassign-btn' : 'tutor-course-assign-btn';
            const buttonText = course.isEnrolled ? tutor_student_course_assignment.i18n.unassign : tutor_student_course_assignment.i18n.assign;
            const buttonColor = course.isEnrolled ? 'tutor-btn-outline-danger' : 'tutor-btn-outline-primary';
            const itemClass = course.isEnrolled ? 'tutor-course-assignment-enrolled' : '';

            // Get thumbnail URL or use placeholder
            const thumbnailUrl = course.thumbnail_url || 'https://via.placeholder.com/80x60';

            html += `
                <div class="tutor-course-assignment-item ${itemClass}">
                    <div class="tutor-course-assignment-info">
                        <div class="tutor-course-assignment-thumbnail">
                            <img src="${thumbnailUrl}" alt="${course.post_title}">
                        </div>
                        <h4 class="tutor-course-assignment-title">${course.post_title}</h4>
                    </div>
                    <div class="tutor-course-assignment-actions">
                        <button class="tutor-btn tutor-btn-sm ${buttonColor} ${buttonClass}" data-course-id="${course.ID}">
                            ${buttonText}
                        </button>
                    </div>
                </div>
            `;
        });

        $('.tutor-course-assignment-list').html(html);
    }

    /**
     * Assign course to student
     *
     * @param {number} courseId - The course ID
     * @param {number} studentId - The student ID
     */
    function assignCourseToStudent(courseId, studentId) {
        const button = $(`.tutor-course-assign-btn[data-course-id="${courseId}"]`);
        button.prop('disabled', true).text(tutor_student_course_assignment.i18n.processing);

        // Log for debugging
        console.log('Assigning course ID:', courseId, 'to student ID:', studentId);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_assign_course_to_student',
                course_id: courseId,
                student_id: studentId,
                _wpnonce: tutor_student_course_assignment.nonce
            },
            success: function(response) {
                console.log('Assign course response:', response);

                if (response.success) {
                    button.removeClass('tutor-course-assign-btn tutor-btn-outline-primary')
                          .addClass('tutor-course-unassign-btn tutor-btn-outline-danger')
                          .text(tutor_student_course_assignment.i18n.unassign)
                          .prop('disabled', false);

                    // Update the course count in the table
                    updateCourseCount(studentId);

                    // Check if it's a paid course with WooCommerce order
                    if (response.data && response.data.is_paid === true) {
                        // Show a message about the WooCommerce order
                        alert(response.data.message + '\n\nSipariş ID: ' + response.data.order_id + '\n\nSiparişler ekranında bu siparişi görebilirsiniz.');
                    } else {
                        // Show a simple success message for free courses
                        alert(response.data.message || 'Öğrenci kursa başarıyla kaydedildi.');
                    }
                } else {
                    button.prop('disabled', false).text(tutor_student_course_assignment.i18n.assign);
                    alert(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Assign course error:', status, error);
                button.prop('disabled', false).text(tutor_student_course_assignment.i18n.assign);
                alert(tutor_student_course_assignment.i18n.error);
            }
        });
    }

    /**
     * Unassign course from student
     *
     * @param {number} courseId - The course ID
     * @param {number} studentId - The student ID
     */
    function unassignCourseFromStudent(courseId, studentId) {
        const button = $(`.tutor-course-unassign-btn[data-course-id="${courseId}"]`);
        button.prop('disabled', true).text(tutor_student_course_assignment.i18n.processing);

        // Log for debugging
        console.log('Unassigning course ID:', courseId, 'from student ID:', studentId);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_unassign_course_from_student',
                course_id: courseId,
                student_id: studentId,
                _wpnonce: tutor_student_course_assignment.nonce
            },
            success: function(response) {
                console.log('Unassign course response:', response);

                if (response.success) {
                    button.removeClass('tutor-course-unassign-btn tutor-btn-outline-danger')
                          .addClass('tutor-course-assign-btn tutor-btn-outline-primary')
                          .text(tutor_student_course_assignment.i18n.assign)
                          .prop('disabled', false);

                    // Update the course count in the table
                    updateCourseCount(studentId);
                } else {
                    button.prop('disabled', false).text(tutor_student_course_assignment.i18n.unassign);
                    alert(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Unassign course error:', status, error);
                button.prop('disabled', false).text(tutor_student_course_assignment.i18n.unassign);
                alert(tutor_student_course_assignment.i18n.error);
            }
        });
    }

    /**
     * Update course count in the table
     *
     * @param {number} studentId - The student ID
     */
    function updateCourseCount(studentId) {
        // Log for debugging
        console.log('Updating course count for student ID:', studentId);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'tutor_get_student_course_count',
                student_id: studentId,
                _wpnonce: tutor_student_course_assignment.nonce
            },
            success: function(response) {
                console.log('Update course count response:', response);

                if (response.success) {
                    $(`tr[data-student-id="${studentId}"] .tutor-course-count`).text(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Update course count error:', status, error);
            }
        });
    }

})(jQuery);
