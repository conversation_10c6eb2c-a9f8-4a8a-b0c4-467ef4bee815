/**
 * Custom Quiz Styles CSS
 * <PERSON><PERSON>, Tutor L<PERSON> quiz sayfalarındaki öğelerin stillerini özelleştirmek için kullanılır.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

/* Quiz buton grubu için margin ayarı */
#tutor-quiz-image-matching-choice > div.tutor-quiz-btn-grp.tutor-mt-32 {
    margin-top: 32px !important; /* Varsayılan margin-top değeri korundu */
    margin-bottom: 50px !important; /* Yeni margin-bottom değeri eklendi */
}

/* Quiz butonları için genel stil düzenlemeleri */
.tutor-quiz-btn-grp {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 15px;
}

/* Quiz butonları için hover efekti */
.tutor-quiz-btn-grp .tutor-btn:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Quiz butonları için aktif durum */
.tutor-quiz-btn-grp .tutor-btn:active {
    transform: translateY(0);
}

/* Dark mode için quiz buton grubu stilleri */
body.tutor-dark-mode .tutor-quiz-btn-grp .tutor-btn,
html[data-theme="dark"] .tutor-quiz-btn-grp .tutor-btn {
    background-color: #2A2A2A;
    border-color: #3A3A3A;
    color: #f5f5f5;
}

/* Dark mode için quiz buton hover durumu */
body.tutor-dark-mode .tutor-quiz-btn-grp .tutor-btn:hover,
html[data-theme="dark"] .tutor-quiz-btn-grp .tutor-btn:hover {
    background-color: #3A3A3A;
    border-color: #4A4A4A;
}
