<?php
/**
 * Custom Admin Bar Settings
 * Bu dosya, WordPress admin bar'ı ile ilgili ayarları içerir.
 * Admin dışındaki kullanıcılar için admin bar'ı devre dışı bırakır.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin dışındaki kullanıcılar için admin bar'ı devre dışı bırakır
 *
 * Bu fonksiyon, admin dışındaki kullanıcılar için admin bar'ı devre dışı bırakır.
 * Ayrıca kullanıcı meta verilerini güncelleyerek kalıcı olarak devre dışı bırakır.
 *
 * @since 1.0.6
 */
function dmr_lms_disable_admin_bar_for_non_admins() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Kullanıcı admin mi kontrol et
    if (!current_user_can('administrator')) {
        // Admin bar'ı devre dışı bırak
        show_admin_bar(false);

        // Kullanıcı meta verilerini güncelle
        $user_id = get_current_user_id();
        update_user_meta($user_id, 'show_admin_bar_front', 'false');
    }
}
add_action('after_setup_theme', 'dmr_lms_disable_admin_bar_for_non_admins');

/**
 * Kullanıcı profil sayfasındaki admin bar seçeneğini gizler
 *
 * Bu fonksiyon, kullanıcı profil sayfasındaki admin bar seçeneğini gizlemek için
 * CSS ekler. Sadece admin dışındaki kullanıcılar için çalışır.
 *
 * @since 1.0.6
 */
function dmr_lms_hide_admin_bar_option_css() {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        return;
    }

    // Kullanıcı admin mi kontrol et
    if (!current_user_can('administrator')) {
        // CSS ekle
        echo '<style>
            /* Araç çubuğu seçeneğini gizle */
            .user-admin-bar-front-wrap,
            tr.user-admin-bar-front-wrap,
            label[for="admin_bar_front"] {
                display: none !important;
            }
        </style>';
    }
}
add_action('admin_head', 'dmr_lms_hide_admin_bar_option_css');
add_action('wp_head', 'dmr_lms_hide_admin_bar_option_css');

/**
 * Yeni kullanıcılar için admin bar'ı varsayılan olarak devre dışı bırakır
 *
 * Bu fonksiyon, yeni oluşturulan kullanıcılar için admin bar'ı varsayılan olarak
 * devre dışı bırakır. Sadece admin dışındaki kullanıcılar için çalışır.
 *
 * @since 1.0.6
 * @param int $user_id Yeni oluşturulan kullanıcının ID'si
 */
function dmr_lms_disable_admin_bar_for_new_users($user_id) {
    // Kullanıcı admin mi kontrol et
    $user = get_userdata($user_id);
    if ($user && !in_array('administrator', $user->roles)) {
        // Admin bar'ı devre dışı bırak
        update_user_meta($user_id, 'show_admin_bar_front', 'false');
    }
}
add_action('user_register', 'dmr_lms_disable_admin_bar_for_new_users');
