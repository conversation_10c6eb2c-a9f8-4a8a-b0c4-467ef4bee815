/**
 * Custom Mobile Fixes CSS
 * Bu dosya, mobil cihaz<PERSON> (özellikle iPhone) görünüm sorunlarını düzeltmek için kullanılır.
 *
 * @package DmrLMS
 * @since 1.0.7
 */

/* Tablet cihazlar için hover efektini kaldır */
@media (min-width: 768px) and (max-width: 1024px) {
    .tutor-theme-mode-button:hover .tutor-theme-mode-icon,
    .tutor-theme-mode-button:active .tutor-theme-mode-icon,
    .tutor-theme-mode-button:focus .tutor-theme-mode-icon {
        transform: none !important;
    }
}

/* iPhone ve küçük mobil cihazlar için özel ayarlar */
@media (max-width: 767px) {
    /* Dark mod butonu için özel ayarlar */
    .tutor-theme-mode-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-right: 10px !important;
        position: relative !important;
        cursor: pointer !important;
    }

    .tutor-theme-mode-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        border-radius: 50% !important;
        background-color: transparent !important;
        border: none !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        color: var(--tutor-color-primary) !important;
        /* Dokunmatik kullanım için daha büyük tıklanabilir alan */
        padding: 8px !important;
    }

    /* Mobil cihazlarda hover efektini kaldır */
    .tutor-theme-mode-button:hover .tutor-theme-mode-icon,
    .tutor-theme-mode-button:active .tutor-theme-mode-icon,
    .tutor-theme-mode-button:focus .tutor-theme-mode-icon {
        transform: none !important;
    }

    .tutor-theme-mode-icon {
        width: 26px !important;
        height: 26px !important;
        stroke-width: 2.2 !important;
    }

    /* Header içindeki butonlar için özel ayarlar */
    .tutor-header-right-side {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-end !important;
        padding-right: 10px !important;
    }

    /* Avatar ve profil alanı düzenlemeleri */
    .tutor-dashboard-header-avatar {
        margin-left: 10px !important;
    }

    /* Avatar boyutunu ayarla */
    .tutor-avatar-xl,
    .tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
        width: 40px !important;
        height: 40px !important;
    }
}

/* Çok küçük ekranlar için (iPhone SE gibi) */
@media (max-width: 375px) {
    .tutor-theme-mode-button {
        width: 42px !important;
        height: 42px !important;
    }

    .tutor-theme-mode-icon {
        width: 24px !important;
        height: 24px !important;
    }

    /* Daha küçük ekranlarda header içindeki öğeleri düzenle */
    .tutor-header-right-side {
        padding-right: 5px !important;
    }

    .tutor-theme-mode-toggle {
        margin-right: 5px !important;
    }

    .tutor-dashboard-header-avatar {
        margin-left: 5px !important;
    }
}
