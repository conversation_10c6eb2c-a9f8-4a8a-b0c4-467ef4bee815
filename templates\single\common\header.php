<?php
/**
 * Common header template.
 *
 * @package Tutor\Templates
 * @subpackage Single\Common
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

use TUTOR\Course;
use Tutor\Models\CourseModel;

$user_id            = get_current_user_id();
$course_id          = isset( $course_id ) ? (int) $course_id : 0;
$is_enrolled        = tutor_utils()->is_enrolled( $course_id );
$course_stats       = tutor_utils()->get_course_completed_percent( $course_id, 0, true );
$show_mark_complete = isset( $mark_as_complete ) ? $mark_as_complete : false;


$is_course_completed = tutor_utils()->is_completed_course( $course_id, $user_id );

/**
 * Auto course complete on all lesson, quiz, assignment complete
 *
 * @since 2.0.7
 * @since 2.4.0 update and refactor.
 */
if ( CourseModel::can_autocomplete_course( $course_id, $user_id ) ) {
	CourseModel::mark_course_as_completed( $course_id, $user_id );

	/**
	 * After auto complete the course.
	 * Set review popup data and redirect to course details page.
	 * Review popup will be shown on course details page.
	 *
	 * @since 2.4.0
	 */
	Course::set_review_popup_data( $user_id, $course_id );
	$course_link = get_permalink( $course_id );
	if ( $course_link ) {
		tutils()->redirect_to( $course_link );
		exit;
	}
}

// Footer için navigasyon butonları
$course_content_id = get_the_ID();
$content_id        = tutor_utils()->get_post_id( $course_content_id );
$contents          = tutor_utils()->get_course_prev_next_contents_by_id( $content_id );
$previous_id       = $contents->previous_id;
$next_id           = $contents->next_id;

$prev_is_preview = get_post_meta( $previous_id, '_is_preview', true );
$next_is_preview = get_post_meta( $next_id, '_is_preview', true );
$is_public       = get_post_meta( $course_id, '_tutor_is_public_course', true );
$prev_is_locked  = ! ( $is_enrolled || $prev_is_preview || $is_public );
$next_is_locked  = ! ( $is_enrolled || $next_is_preview || $is_public );
$prev_link       = $prev_is_locked || ! $previous_id ? '#' : get_the_permalink( $previous_id );
$next_link       = $next_is_locked || ! $next_id ? '#' : get_the_permalink( $next_id );

?>
<div class="tutor-course-topic-single-header tutor-single-page-top-bar">
	<div class="tutor-align-center tutor-d-flex tutor-d-xl-none">
		<a class="tutor-iconic-btn tutor-mobile-hamburger-btn" href="#" tutor-course-topics-sidebar-offcanvas-toggler>
			<span class="tutor-icon-hamburger-menu" area-hidden="true"></span>
		</a>
		<?php if ( $is_enrolled && $show_mark_complete ) : ?>
			<div class="tutor-mobile-complete-btn">
				<?php tutor_lesson_mark_complete_html(); ?>
			</div>
		<?php endif; ?>
	</div>

	<a href="#" class="tutor-course-topics-sidebar-toggler tutor-iconic-btn tutor-iconic-btn-secondary tutor-d-none tutor-d-xl-inline-flex tutor-flex-shrink-0" tutor-course-topics-sidebar-toggler>
		<span class="tutor-icon-hamburger-menu" area-hidden="true"></span>
	</a>

	<div class="tutor-course-topic-single-header-title tutor-fs-6 tutor-ml-12 tutor-ml-xl-24">
		<?php echo esc_html( get_the_title( $course_id ) ); ?>
	</div>

	<div class="tutor-ml-auto tutor-align-center tutor-d-none tutor-d-xl-flex">
		<?php if ( $is_enrolled ) : ?>
			<?php do_action( 'tutor_course/single/enrolled/before/lead_info/progress_bar' ); ?>
			<?php /* İlerleme bilgisi kaldırıldı - Progress information removed */ ?>
			<?php do_action( 'tutor_course/single/enrolled/after/lead_info/progress_bar' ); ?>
			<?php
			/* Masaüstü görünümünde tamamlama butonu aktif edildi */
			if ( $show_mark_complete ) {
				tutor_lesson_mark_complete_html();
			}
			do_action( 'tutor_after_lesson_completion_button', $course_id, $user_id, $is_course_completed, $course_stats );
			?>
		<?php endif; ?>

		<?php if ( $next_id || $previous_id ) : ?>
			<div class="tutor-single-course-content-prev tutor-mr-8">
				<a class="tutor-btn tutor-btn-secondary tutor-btn-sm" href="<?php echo esc_url( $prev_link ); ?>"<?php echo ! $previous_id ? ' disabled="disabled"' : ''; ?>>
					<span class="tutor-icon-<?php echo is_rtl() ? 'next' : 'previous'; ?>" area-hidden="true"></span>
					<span class="tutor-ml-8"><?php esc_html_e( 'Previous', 'tutor' ); ?></span>
				</a>
			</div>

			<div class="tutor-single-course-content-next tutor-mr-8">
				<a class="tutor-btn tutor-btn-secondary tutor-btn-sm" href="<?php echo esc_url( $next_link ); ?>"<?php echo ! $next_id ? ' disabled="disabled"' : ''; ?>>
					<span class="tutor-mr-8"><?php esc_html_e( 'Next', 'tutor' ); ?></span>
					<span class="tutor-icon-<?php echo is_rtl() ? 'previous' : 'next'; ?>" area-hidden="true"></span>
				</a>
			</div>
		<?php endif; ?>

		<?php
		if ( 0 === $course_id && 'tutor_zoom_meeting' === get_post_type( get_the_ID() ) ) {
			// Zoom General Meeting.
			$course_id = wp_get_post_parent_id( get_the_ID() );
		}
		?>
		<a class="tutor-iconic-btn tutor-flex-shrink-0" href="<?php echo esc_url( get_the_permalink( $course_id ) ); ?>">
			<span class="tutor-icon-times" area-hidden="true"></span>
		</a>
	</div>

	<a href="<?php echo esc_url( get_the_permalink( $course_id ) ); ?>" class="tutor-iconic-btn tutor-d-flex tutor-d-xl-none tutor-ml-auto">
		<span class="tutor-icon-times" area-hidden="true"></span>
	</a>
</div>

<script>
// Başlık metninin üç nokta ile kesilmesini sağlayan JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const titleElement = document.querySelector('.tutor-course-topic-single-header-title.tutor-fs-6.tutor-ml-12.tutor-ml-xl-24');
    const markCompleteBtn = document.querySelector('.tutor-topbar-mark-btn');

    if (titleElement && markCompleteBtn) {
        // Başlık metnini al
        const titleText = titleElement.textContent;
        // title attribute'u ekleyerek hover durumunda tam metnin görünmesini sağla
        titleElement.setAttribute('title', titleText);

        // Not: tutor-marquee sınıfı artık eklenmeyecek
        // Artık başlık CSS'deki text-overflow: ellipsis ile kesilecek
    }
});
</script>
